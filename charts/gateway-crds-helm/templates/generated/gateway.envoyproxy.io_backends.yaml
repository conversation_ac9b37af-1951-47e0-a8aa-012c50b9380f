{{- if .Values.crds.envoyGateway.enabled }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  name: backends.gateway.envoyproxy.io
spec:
  group: gateway.envoyproxy.io
  names:
    categories:
    - envoy-gateway
    kind: Backend
    listKind: BackendList
    plural: backends
    shortNames:
    - be
    singular: backend
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[?(@.type=="Accepted")].reason
      name: Status
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          Backend allows the user to configure the endpoints of a backend and
          the behavior of the connection from Envoy Proxy to the backend.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the desired state of Backend.
            properties:
              appProtocols:
                description: AppProtocols defines the application protocols to be
                  supported when connecting to the backend.
                items:
                  description: AppProtocolType defines various backend applications
                    protocols supported by Envoy Gateway
                  enum:
                  - gateway.envoyproxy.io/h2c
                  - gateway.envoyproxy.io/ws
                  - gateway.envoyproxy.io/wss
                  type: string
                type: array
              endpoints:
                description: Endpoints defines the endpoints to be used when connecting
                  to the backend.
                items:
                  description: |-
                    BackendEndpoint describes a backend endpoint, which can be either a fully-qualified domain name, IP address or unix domain socket
                    corresponding to Envoy's Address: https://www.envoyproxy.io/docs/envoy/latest/api-v3/config/core/v3/address.proto#config-core-v3-address
                  properties:
                    fqdn:
                      description: FQDN defines a FQDN endpoint
                      properties:
                        hostname:
                          description: Hostname defines the FQDN hostname of the backend
                            endpoint.
                          maxLength: 253
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        port:
                          description: Port defines the port of the backend endpoint.
                          format: int32
                          maximum: 65535
                          minimum: 0
                          type: integer
                      required:
                      - hostname
                      - port
                      type: object
                    ip:
                      description: IP defines an IP endpoint. Supports both IPv4 and
                        IPv6 addresses.
                      properties:
                        address:
                          description: |-
                            Address defines the IP address of the backend endpoint.
                            Supports both IPv4 and IPv6 addresses.
                          maxLength: 45
                          minLength: 3
                          pattern: ^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{1,4}|::|(([0-9a-fA-F]{1,4}:){0,5})?(:[0-9a-fA-F]{1,4}){1,2})$
                          type: string
                        port:
                          description: Port defines the port of the backend endpoint.
                          format: int32
                          maximum: 65535
                          minimum: 0
                          type: integer
                      required:
                      - address
                      - port
                      type: object
                    unix:
                      description: Unix defines the unix domain socket endpoint
                      properties:
                        path:
                          description: |-
                            Path defines the unix domain socket path of the backend endpoint.
                            The path length must not exceed 108 characters.
                          type: string
                          x-kubernetes-validations:
                          - message: unix domain socket path must not exceed 108 characters
                            rule: size(self) <= 108
                      required:
                      - path
                      type: object
                    zone:
                      description: Zone defines the service zone of the backend endpoint.
                      type: string
                  type: object
                  x-kubernetes-validations:
                  - message: one of fqdn, ip or unix must be specified
                    rule: (has(self.fqdn) || has(self.ip) || has(self.unix))
                  - message: only one of fqdn, ip or unix can be specified
                    rule: ((has(self.fqdn) && !(has(self.ip) || has(self.unix))) ||
                      (has(self.ip) && !(has(self.fqdn) || has(self.unix))) || (has(self.unix)
                      && !(has(self.ip) || has(self.fqdn))))
                maxItems: 64
                minItems: 1
                type: array
                x-kubernetes-validations:
                - message: fqdn addresses cannot be mixed with other address types
                  rule: self.all(f, has(f.fqdn)) || !self.exists(f, has(f.fqdn))
              fallback:
                description: |-
                  Fallback indicates whether the backend is designated as a fallback.
                  It is highly recommended to configure active or passive health checks to ensure that failover can be detected
                  when the active backends become unhealthy and to automatically readjust once the primary backends are healthy again.
                  The overprovisioning factor is set to 1.4, meaning the fallback backends will only start receiving traffic when
                  the health of the active backends falls below 72%.
                type: boolean
              hostOverrideSettings:
                description: HostOverrideSettings defines the host override settings
                  for the backend.
                properties:
                  overrideHostSources:
                    description: |-
                      OverrideHostSources defines the sources to get host addresses from for endpoint selection.
                      The host sources are searched in the order specified. The request is forwarded to the first
                      address and subsequent addresses are used for request retries or hedging.
                    items:
                      description: OverrideHostSource defines a source for getting
                        override host addresses.
                      properties:
                        header:
                          description: |-
                            Header specifies the header name to get the override host addresses from.
                            The header value should contain host addresses in "IP:Port" format or multiple
                            addresses separated by commas like "IP:Port,IP:Port,...".
                            For example: "********:8080" or "[2600:4040:5204::1574:24ae]:80".
                          minLength: 1
                          type: string
                        metadata:
                          description: |-
                            Metadata specifies the metadata key to get the override host addresses from
                            the request dynamic metadata. The metadata value should contain host addresses
                            in the same format as the header field.
                          properties:
                            key:
                              description: Key is the top-level metadata key.
                              minLength: 1
                              type: string
                            path:
                              description: |-
                                Path defines the path within the metadata to access the value.
                                Each element in the path represents a key in nested metadata structure.
                              items:
                                description: MetadataPathSegment represents a segment
                                  in the metadata path.
                                properties:
                                  key:
                                    description: Key is the metadata key for this
                                      path segment.
                                    minLength: 1
                                    type: string
                                required:
                                - key
                                type: object
                              type: array
                          required:
                          - key
                          type: object
                      type: object
                      x-kubernetes-validations:
                      - message: exactly one of header or metadata must be specified
                        rule: (has(self.header) && !has(self.metadata)) || (!has(self.header)
                          && has(self.metadata))
                    maxItems: 8
                    minItems: 1
                    type: array
                required:
                - overrideHostSources
                type: object
              tls:
                description: |-
                  TLS defines the TLS settings for the backend.
                  TLS.CACertificateRefs and TLS.WellKnownCACertificates can only be specified for DynamicResolver backends.
                  TLS.InsecureSkipVerify can be specified for any Backends
                properties:
                  caCertificateRefs:
                    description: |-
                      CACertificateRefs contains one or more references to Kubernetes objects that
                      contain TLS certificates of the Certificate Authorities that can be used
                      as a trust anchor to validate the certificates presented by the backend.

                      A single reference to a Kubernetes ConfigMap or a Kubernetes Secret,
                      with the CA certificate in a key named `ca.crt` is currently supported.

                      If CACertificateRefs is empty or unspecified, then WellKnownCACertificates must be
                      specified. Only one of CACertificateRefs or WellKnownCACertificates may be specified,
                      not both.

                      Only used for DynamicResolver backends.
                    items:
                      description: |-
                        LocalObjectReference identifies an API object within the namespace of the
                        referrer.
                        The API object must be valid in the cluster; the Group and Kind must
                        be registered in the cluster for this reference to be valid.

                        References to objects with invalid Group and Kind are not valid, and must
                        be rejected by the implementation, with appropriate Conditions set
                        on the containing object.
                      properties:
                        group:
                          description: |-
                            Group is the group of the referent. For example, "gateway.networking.k8s.io".
                            When unspecified or empty string, core API group is inferred.
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          description: Kind is kind of the referent. For example "HTTPRoute"
                            or "Service".
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          description: Name is the name of the referent.
                          maxLength: 253
                          minLength: 1
                          type: string
                      required:
                      - group
                      - kind
                      - name
                      type: object
                    maxItems: 8
                    type: array
                  insecureSkipVerify:
                    default: false
                    description: |-
                      InsecureSkipVerify indicates whether the upstream's certificate verification
                      should be skipped. Defaults to "false".
                    type: boolean
                  wellKnownCACertificates:
                    description: |-
                      WellKnownCACertificates specifies whether system CA certificates may be used in
                      the TLS handshake between the gateway and backend pod.

                      If WellKnownCACertificates is unspecified or empty (""), then CACertificateRefs
                      must be specified with at least one entry for a valid configuration. Only one of
                      CACertificateRefs or WellKnownCACertificates may be specified, not both.

                      Only used for DynamicResolver backends.
                    enum:
                    - System
                    type: string
                type: object
                x-kubernetes-validations:
                - message: must not contain both CACertificateRefs and WellKnownCACertificates
                  rule: '!(has(self.caCertificateRefs) && size(self.caCertificateRefs)
                    > 0 && has(self.wellKnownCACertificates) && self.wellKnownCACertificates
                    != "")'
                - message: must not contain either CACertificateRefs or WellKnownCACertificates
                    when InsecureSkipVerify is enabled
                  rule: '!((has(self.insecureSkipVerify) && self.insecureSkipVerify)
                    && ((has(self.caCertificateRefs) && size(self.caCertificateRefs)
                    > 0) || (has(self.wellKnownCACertificates) && self.wellKnownCACertificates
                    != "")))'
              type:
                default: Endpoints
                description: Type defines the type of the backend. Defaults to "Endpoints"
                enum:
                - Endpoints
                - DynamicResolver
                - HostOverride
                type: string
            type: object
            x-kubernetes-validations:
            - message: DynamicResolver type cannot have endpoints specified
              rule: self.type != 'DynamicResolver' || !has(self.endpoints)
            - message: HostOverride type must have hostOverrideSettings specified
              rule: self.type != 'HostOverride' || has(self.hostOverrideSettings)
            - message: HostOverride type must have endpoints specified as fallback
                services
              rule: self.type != 'HostOverride' || has(self.endpoints)
          status:
            description: Status defines the current status of Backend.
            properties:
              conditions:
                description: Conditions describe the current conditions of the Backend.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}
