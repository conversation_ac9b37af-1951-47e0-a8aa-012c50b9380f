- clusterName: first-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: fallback1.default.svc.cluster.local
            portValue: 80
      loadBalancingWeight: 1
    - endpoint:
        address:
          socketAddress:
            address: fallback2.default.svc.cluster.local
            portValue: 80
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: backend-hosts-override-with-fallback
- clusterName: second-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: ************
            portValue: 8080
      loadBalancingWeight: 1
    - endpoint:
        address:
          socketAddress:
            address: ************
            portValue: 8080
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: backend-hosts-override-metadata-with-fallback
